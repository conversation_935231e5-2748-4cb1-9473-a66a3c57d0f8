// API client for backend communication

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:5000/api';

// Helper function to get auth token from cookies
function getAuthToken(): string | null {
  if (typeof document === 'undefined') return null;
  const cookies = document.cookie.split(';');
  const tokenCookie = cookies.find(cookie => cookie.trim().startsWith('token='));
  return tokenCookie ? tokenCookie.split('=')[1] : null;
}

export async function fetchFromAPI(endpoint: string, options: RequestInit = {}) {
  const token = getAuthToken();

  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
    },
  };

  const mergedOptions = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  };

  const response = await fetch(`${API_BASE_URL}${endpoint}`, mergedOptions);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: response.statusText }));
    throw new Error(errorData.message || `API error: ${response.statusText}`);
  }

  return response.json();
}

// Auth related API calls
export const authAPI = {
  login: async (credentials: { email: string; password: string }) => {
    return fetchFromAPI('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  },

  register: async (userData: {
    email: string;
    password: string;
    name: string;
    confirmPassword: string;
  }) => {
    return fetchFromAPI('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  },

  logout: async () => {
    return fetchFromAPI('/auth/logout', {
      method: 'POST',
    });
  },

  forgotPassword: async (email: string) => {
    return fetchFromAPI('/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  },

  resetPassword: async (token: string, password: string) => {
    return fetchFromAPI(`/auth/reset-password/${token}`, {
      method: 'PUT',
      body: JSON.stringify({ password }),
    });
  },
};

// User related API calls
export const userAPI = {
  getProfile: async () => {
    return fetchFromAPI('/users/profile');
  },

  updateProfile: async (userData: { name?: string; email?: string }) => {
    return fetchFromAPI('/users/profile', {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  },

  changePassword: async (currentPassword: string, newPassword: string) => {
    return fetchFromAPI('/users/change-password', {
      method: 'PUT',
      body: JSON.stringify({ currentPassword, newPassword }),
    });
  },

  getSubscriptionStatus: async () => {
    return fetchFromAPI('/users/subscription-status');
  },
};

// Subscription related API calls
export const subscriptionAPI = {
  getPricing: async () => {
    return fetchFromAPI('/subscriptions/pricing');
  },

  createSubscription: async (subscriptionData: any) => {
    return fetchFromAPI('/subscriptions', {
      method: 'POST',
      body: JSON.stringify(subscriptionData),
    });
  },

  getActiveSubscription: async () => {
    return fetchFromAPI('/subscriptions/active');
  },

  getSubscriptionHistory: async () => {
    return fetchFromAPI('/subscriptions/history');
  },

  cancelSubscription: async () => {
    return fetchFromAPI('/subscriptions/cancel', {
      method: 'DELETE',
    });
  },
};

// Calculation related API calls
export const calculationAPI = {
  calculateMaritime: async (data: any) => {
    return fetchFromAPI('/calculations/maritime/calculate', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  testMaritime: async (data: any) => {
    return fetchFromAPI('/calculations/maritime/test', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },





  getPortSuggestions: async () => {
    return fetchFromAPI('/calculations/utils/ports');
  },

  getExchangeRates: async () => {
    return fetchFromAPI('/calculations/utils/exchange-rates');
  },

  getVesselTypes: async () => {
    return fetchFromAPI('/calculations/utils/vessel-types');
  },

  getCargoTypes: async () => {
    return fetchFromAPI('/calculations/utils/cargo-types');
  },

  getPortCallTypes: async () => {
    return fetchFromAPI('/calculations/utils/port-call-types');
  },

  getFlagCategories: async () => {
    return fetchFromAPI('/calculations/utils/flag-categories');
  },

  generatePDF: async (result: any, vesselName?: string) => {
    const token = getAuthToken();

    const response = await fetch(`${API_BASE_URL}/calculations/generate-pdf`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
      },
      body: JSON.stringify({ result, vesselName }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: response.statusText }));
      throw new Error(errorData.message || `PDF generation failed: ${response.statusText}`);
    }

    // Return the blob for download
    return response.blob();
  },
};

// Payment related API calls
export const paymentAPI = {
  initializeSubscriptionPayment: async (subscriptionType: string) => {
    return fetchFromAPI('/payments/subscription/initialize', {
      method: 'POST',
      body: JSON.stringify({ subscriptionType }),
    });
  },

  initializeProformaPayment: async (calculationId: string, amount: number, currency: string = 'TRY') => {
    return fetchFromAPI('/payments/proforma/initialize', {
      method: 'POST',
      body: JSON.stringify({ calculationId, amount, currency }),
    });
  },

  getPaymentStatus: async (paymentId: string, conversationId: string) => {
    return fetchFromAPI(`/payments/status/${paymentId}/${conversationId}`);
  },

  getInstallmentInfo: async () => {
    return fetchFromAPI('/payments/installment-info');
  },
};

// Legacy API object for backward compatibility
export const api = {
  auth: {
    login: async (email: string, password: string) => {
      return authAPI.login({ email, password });
    },
    register: authAPI.register,
    logout: authAPI.logout,
  },
  user: userAPI,
  subscription: subscriptionAPI,
  calculation: calculationAPI,
  payment: paymentAPI,
};