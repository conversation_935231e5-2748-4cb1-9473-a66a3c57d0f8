'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { calculationAPI, paymentAPI } from '@/lib/api';

interface MaritimeCalculationData {
  vesselName: string;
  grt: number;
  dwt: number;
  loa: number;
  beam: number;
  draft: number;
  portOfLoading: string;
  portOfDischarge: string;
  cargoType: string;
  cargoWeight: number;
  isDangerous: boolean;
  currency: string;
  // Additional fields required by backend
  portName?: string;
  portCallType?: string;
  vesselType?: string;
  flagCategory?: string;
  portLocation?: string;
  grossRegisterTonnage?: number;
  netRegisterTonnage?: number;
  daysAtQuay?: number;
  usdTryRate?: number;
}

interface CalculationResult {
  // Keep the simplified structure for display, but also store the full result
  pilotage?: {
    calculation: string;
    total: number;
  };
  tugboat?: {
    calculation: string;
    total: number;
  };
  portDues?: {
    calculation: string;
    total: number;
  };
  agency?: {
    calculation: string;
    total: number;
  };
  total?: number;
  currency?: string;

  // Store the full backend result for PDF generation
  fullResult?: any;
}

interface DropdownOption {
  value: string;
  label: string;
}

interface Port {
  name: string;
  location: string;
}

export default function MaritimeCalculation() {
  const [formData, setFormData] = useState<MaritimeCalculationData>({
    vesselName: '',
    grt: 0,
    dwt: 0,
    loa: 0,
    beam: 0,
    draft: 0,
    portOfLoading: '',
    portOfDischarge: '',
    cargoType: '',
    cargoWeight: 0,
    isDangerous: false,
    currency: 'USD',
    // Additional fields required by backend
    portName: '',
    portCallType: '',
    vesselType: '',
    flagCategory: '',
    portLocation: '',
    grossRegisterTonnage: 0,
    netRegisterTonnage: 0,
    daysAtQuay: 1,
    usdTryRate: 30.0
  });

  const [result, setResult] = useState<CalculationResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const [paymentLoading, setPaymentLoading] = useState(false);
  const [pdfLoading, setPdfLoading] = useState(false);
  const router = useRouter();

  // Dropdown data
  const [ports, setPorts] = useState<Port[]>([]);
  const [vesselTypes, setVesselTypes] = useState<DropdownOption[]>([]);
  const [cargoTypes, setCargoTypes] = useState<DropdownOption[]>([]);
  const [portCallTypes, setPortCallTypes] = useState<DropdownOption[]>([]);
  const [flagCategories, setFlagCategories] = useState<DropdownOption[]>([]);

  useEffect(() => {
    // Check if user is authenticated
    const token = document.cookie.split(';').find(cookie => cookie.trim().startsWith('token='));
    if (!token) {
      router.push('/login');
      return;
    }

    // Load dropdown data
    const loadDropdownData = async () => {
      try {
        const [portsRes, vesselTypesRes, cargoTypesRes, portCallTypesRes, flagCategoriesRes] = await Promise.all([
          calculationAPI.getPortSuggestions(),
          calculationAPI.getVesselTypes(),
          calculationAPI.getCargoTypes(),
          calculationAPI.getPortCallTypes(),
          calculationAPI.getFlagCategories()
        ]);

        setPorts(portsRes.data);
        setVesselTypes(vesselTypesRes.data);
        setCargoTypes(cargoTypesRes.data);
        setPortCallTypes(portCallTypesRes.data);
        setFlagCategories(flagCategoriesRes.data);
      } catch (error) {
        console.error('Failed to load dropdown data:', error);
      }
    };

    loadDropdownData();
  }, [router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const newValue = type === 'checkbox' ? (e.target as HTMLInputElement).checked :
                     type === 'number' ? parseFloat(value) || 0 : value;

    setFormData(prev => {
      const updated = {
        ...prev,
        [name]: newValue
      };

      // Sync GRT with grossRegisterTonnage
      if (name === 'grt') {
        updated.grossRegisterTonnage = newValue as number;
      } else if (name === 'grossRegisterTonnage') {
        updated.grt = newValue as number;
      }

      // Set port location when port is selected
      if (name === 'portOfLoading' || name === 'portName') {
        const selectedPort = ports.find(port => port.name === value);
        if (selectedPort) {
          updated.portLocation = selectedPort.location;
          updated.portName = selectedPort.name;
          updated.portOfLoading = selectedPort.name;
        }
      }

      return updated;
    });
  };

  const handleCalculate = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      // Transform frontend form data to backend expected format
      const backendData = {
        portName: formData.portName || formData.portOfLoading,
        portLocation: formData.portLocation || 'Istanbul', // Default if not set
        portCallType: formData.portCallType || 'simple-port-call',
        vesselType: formData.vesselType || 'other',
        flagCategory: formData.flagCategory || 'foreign',
        grossRegisterTonnage: formData.grossRegisterTonnage || formData.grt,
        netRegisterTonnage: formData.netRegisterTonnage || Math.round(formData.grt * 0.7), // Estimate if not provided
        deadweightTonnage: formData.dwt,
        daysAtQuay: formData.daysAtQuay || 1,
        cargoType: formData.cargoType,
        cargoQuantity: formData.cargoWeight,
        cargoCategory: 'general', // Default cargo category
        cargoNature: 'General Cargo',
        usdTryRate: formData.usdTryRate || 30.0,
        isDangerous: formData.isDangerous || false
      };

      const response = await calculationAPI.calculateMaritime(backendData);

      // Store the full result for PDF generation and create simplified version for display
      const fullResult = response.data;
      const simplifiedResult: CalculationResult = {
        pilotage: fullResult.fees?.pilotage ? {
          calculation: fullResult.fees.pilotage.calculation || '',
          total: fullResult.fees.pilotage.total || 0
        } : undefined,
        tugboat: fullResult.fees?.tugboat ? {
          calculation: fullResult.fees.tugboat.calculation || '',
          total: fullResult.fees.tugboat.total || 0
        } : undefined,
        portDues: fullResult.fees?.quayDue ? {
          calculation: fullResult.fees.quayDue.calculation || '',
          total: fullResult.fees.quayDue.total || 0
        } : undefined,
        agency: fullResult.fees?.agencyFee ? {
          calculation: fullResult.fees.agencyFee.calculation || '',
          total: fullResult.fees.agencyFee.usdAmount || 0
        } : undefined,
        total: fullResult.grandTotal || 0,
        currency: fullResult.currency || 'USD',
        fullResult: fullResult
      };

      setResult(simplifiedResult);
    } catch (err: any) {
      console.error('Calculation error:', err);
      setError(err.message || 'Calculation failed');
    } finally {
      setLoading(false);
    }
  };



  const handlePayment = async () => {
    if (!result || !result.total) return;

    setPaymentLoading(true);
    try {
      // Create a temporary calculation ID (in real app, this would come from saved calculation)
      const calculationId = `temp_${Date.now()}`;

      const paymentResponse = await paymentAPI.initializeProformaPayment(
        calculationId,
        result.total,
        result.currency || 'USD'
      );

      if (paymentResponse.success && paymentResponse.data.paymentPageUrl) {
        // Redirect to payment page
        window.open(paymentResponse.data.paymentPageUrl, '_blank');
      } else {
        alert('Payment initialization failed');
      }
    } catch (err: any) {
      console.error('Payment error:', err);
      alert('Failed to initialize payment: ' + (err.message || 'Unknown error'));
    } finally {
      setPaymentLoading(false);
    }
  };

  const handleDownloadPDF = async () => {
    if (!result || !result.fullResult) return;

    setPdfLoading(true);
    try {
      const blob = await calculationAPI.generatePDF(result.fullResult, formData.vesselName);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // Generate filename
      const portName = formData.portName || formData.portOfLoading || 'Port';
      const date = new Date().toISOString().split('T')[0];
      link.download = `Proforma_${portName}_${date}.pdf`;

      // Trigger download
      document.body.appendChild(link);
      link.click();

      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err: any) {
      console.error('PDF download error:', err);
      alert('Failed to download PDF: ' + (err.message || 'Unknown error'));
    } finally {
      setPdfLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <Link href="/dashboard" className="text-blue-600 hover:text-blue-500 text-sm">
                ← Back to Dashboard
              </Link>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mt-2">
                Maritime Proforma Calculation
              </h1>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Calculation Form */}
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
                  Vessel & Cargo Information
                </h3>
                
                {error && (
                  <div className="mb-4 bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 p-4">
                    <p className="text-sm text-red-700 dark:text-red-400">{error}</p>
                  </div>
                )}

                <form onSubmit={handleCalculate} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Port Name
                      </label>
                      <select
                        name="portName"
                        value={formData.portOfLoading}
                        onChange={(e) => setFormData(prev => ({ ...prev, portOfLoading: e.target.value, portName: e.target.value }))}
                        required
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      >
                        <option value="">Select Port</option>
                        {ports.map((port, index) => (
                          <option key={index} value={port.name}>
                            {port.name} - {port.location}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Port Call Type
                      </label>
                      <select
                        name="portCallType"
                        value={formData.portCallType || ''}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      >
                        <option value="">Select Port Call Type</option>
                        {portCallTypes.map((type) => (
                          <option key={type.value} value={type.value}>
                            {type.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Vessel Type
                      </label>
                      <select
                        name="vesselType"
                        value={formData.vesselType || ''}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      >
                        <option value="">Select Vessel Type</option>
                        {vesselTypes.map((type) => (
                          <option key={type.value} value={type.value}>
                            {type.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Flag Category
                      </label>
                      <select
                        name="flagCategory"
                        value={formData.flagCategory || ''}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      >
                        <option value="">Select Flag Category</option>
                        {flagCategories.map((category) => (
                          <option key={category.value} value={category.value}>
                            {category.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Vessel Name
                      </label>
                      <input
                        type="text"
                        name="vesselName"
                        value={formData.vesselName}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        GRT (Gross Tonnage)
                      </label>
                      <input
                        type="number"
                        name="grt"
                        value={formData.grt}
                        onChange={handleInputChange}
                        required
                        min="0"
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        DWT (Deadweight)
                      </label>
                      <input
                        type="number"
                        name="dwt"
                        value={formData.dwt}
                        onChange={handleInputChange}
                        required
                        min="0"
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        NRT (Net Register Tonnage)
                      </label>
                      <input
                        type="number"
                        name="netRegisterTonnage"
                        value={formData.netRegisterTonnage || 0}
                        onChange={handleInputChange}
                        required
                        min="0"
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Days at Quay
                      </label>
                      <input
                        type="number"
                        name="daysAtQuay"
                        value={formData.daysAtQuay || 1}
                        onChange={handleInputChange}
                        required
                        min="1"
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        LOA (Length Overall) - meters
                      </label>
                      <input
                        type="number"
                        name="loa"
                        value={formData.loa}
                        onChange={handleInputChange}
                        required
                        min="0"
                        step="0.1"
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Beam - meters
                      </label>
                      <input
                        type="number"
                        name="beam"
                        value={formData.beam}
                        onChange={handleInputChange}
                        required
                        min="0"
                        step="0.1"
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Draft - meters
                      </label>
                      <input
                        type="number"
                        name="draft"
                        value={formData.draft}
                        onChange={handleInputChange}
                        required
                        min="0"
                        step="0.1"
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Port of Loading
                      </label>
                      <select
                        name="portOfLoading"
                        value={formData.portOfLoading}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      >
                        <option value="">Select Port of Loading</option>
                        {ports.map((port, index) => (
                          <option key={index} value={port.name}>
                            {port.name} - {port.location}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Port of Discharge
                      </label>
                      <select
                        name="portOfDischarge"
                        value={formData.portOfDischarge}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      >
                        <option value="">Select Port of Discharge</option>
                        {ports.map((port, index) => (
                          <option key={index} value={port.name}>
                            {port.name} - {port.location}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Cargo Type
                      </label>
                      <select
                        name="cargoType"
                        value={formData.cargoType}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      >
                        <option value="">Select Cargo Type</option>
                        {cargoTypes.map((type) => (
                          <option key={type.value} value={type.value}>
                            {type.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Cargo Weight (MT)
                      </label>
                      <input
                        type="number"
                        name="cargoWeight"
                        value={formData.cargoWeight}
                        onChange={handleInputChange}
                        required
                        min="0"
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Currency
                      </label>
                      <select
                        name="currency"
                        value={formData.currency}
                        onChange={handleInputChange}
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      >
                        <option value="USD">USD</option>
                        <option value="EUR">EUR</option>
                        <option value="TRY">TRY</option>
                      </select>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        name="isDangerous"
                        checked={formData.isDangerous}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                        Dangerous Cargo
                      </label>
                    </div>
                  </div>

                  <div className="pt-4">
                    <button
                      type="submit"
                      disabled={loading}
                      className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    >
                      {loading ? 'Calculating...' : 'Calculate Proforma'}
                    </button>
                  </div>
                </form>
              </div>
            </div>

            {/* Results */}
            {result && (
              <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                      Calculation Results
                    </h3>
                    <div className="flex space-x-2">
                      <button
                        onClick={handleDownloadPDF}
                        disabled={pdfLoading}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                      >
                        {pdfLoading ? 'Generating...' : 'Download PDF'}
                      </button>

                      <button
                        onClick={handlePayment}
                        disabled={paymentLoading}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                      >
                        {paymentLoading ? 'Processing...' : 'Pay Now'}
                      </button>
                    </div>
                  </div>

                  <div className="space-y-4">
                    {result.pilotage && (
                      <div className="border-l-4 border-blue-500 pl-4">
                        <h4 className="font-medium text-gray-900 dark:text-white">Pilotage</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {result.pilotage.calculation}
                        </p>
                        <p className="text-lg font-semibold text-blue-600">
                          {result.pilotage.total} {result.currency}
                        </p>
                      </div>
                    )}

                    {result.tugboat && (
                      <div className="border-l-4 border-green-500 pl-4">
                        <h4 className="font-medium text-gray-900 dark:text-white">Tugboat</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {result.tugboat.calculation}
                        </p>
                        <p className="text-lg font-semibold text-green-600">
                          {result.tugboat.total} {result.currency}
                        </p>
                      </div>
                    )}

                    {result.portDues && (
                      <div className="border-l-4 border-yellow-500 pl-4">
                        <h4 className="font-medium text-gray-900 dark:text-white">Port Dues</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {result.portDues.calculation}
                        </p>
                        <p className="text-lg font-semibold text-yellow-600">
                          {result.portDues.total} {result.currency}
                        </p>
                      </div>
                    )}

                    {result.agency && (
                      <div className="border-l-4 border-purple-500 pl-4">
                        <h4 className="font-medium text-gray-900 dark:text-white">Agency Fee</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {result.agency.calculation}
                        </p>
                        <p className="text-lg font-semibold text-purple-600">
                          {result.agency.total} {result.currency}
                        </p>
                      </div>
                    )}

                    {result.total && (
                      <div className="border-t pt-4 mt-4">
                        <div className="flex justify-between items-center">
                          <h4 className="text-xl font-bold text-gray-900 dark:text-white">
                            Total Amount
                          </h4>
                          <p className="text-2xl font-bold text-blue-600">
                            {result.total} {result.currency}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
